package to.sunset.litetools

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.window.Tray
import androidx.compose.ui.window.Window
import androidx.compose.ui.window.application
import androidx.compose.ui.window.rememberTrayState
import androidx.compose.ui.window.rememberWindowState
import java.awt.Desktop
import java.awt.Taskbar
import javax.swing.SwingUtilities
import litetools.composeapp.generated.resources.Res
import litetools.composeapp.generated.resources.icon
import org.jetbrains.compose.resources.painterResource

fun main() = application {
    val trayState = rememberTrayState()
    val windowState = rememberWindowState()
    var isVisible by remember { mutableStateOf(true) }
    var isDockVisible by remember { mutableStateOf(true) }

    // 设置 macOS Dock 图标和行为
    setupMacOSDock()

    Tray(
        state = trayState,
        icon = painterResource(Res.drawable.icon),
        tooltip = "LiteTools",
        onAction = {
            // 单击托盘图标显示窗口
            isVisible = true
            isDockVisible = true
            showDockIcon()
        },
        menu = {
            Item("显示", onClick = {
                isVisible = true
                isDockVisible = true
                showDockIcon()
            })
            Item("隐藏到后台", onClick = {
                isVisible = false
                isDockVisible = false
                hideDockIcon()
            })
            Item("关闭", onClick = ::exitApplication)
        }
    )

    if (isVisible) {
        Window(
            onCloseRequest = {
                // 关闭窗口时隐藏到后台，而不是退出应用
                isVisible = false
                isDockVisible = false
                hideDockIcon()
            },
            state = windowState,
            title = "LiteTools",
            icon = painterResource(Res.drawable.icon),
        ) {
            App()
        }
    }
}

private fun setupMacOSDock() {
    try {
        // 检查是否在 macOS 上运行
        val osName = System.getProperty("os.name").lowercase()
        if (osName.contains("mac")) {
            // 设置 macOS 应用程序名称
            System.setProperty("apple.awt.application.name", "LiteTools")

            // 设置 Dock 图标行为
            if (Desktop.isDesktopSupported()) {
                val desktop = Desktop.getDesktop()

                // 设置关于对话框处理器
                if (desktop.isSupported(Desktop.Action.APP_ABOUT)) {
                    desktop.setAboutHandler {
                        // 可以在这里添加关于对话框逻辑
                    }
                }

                // 设置退出处理器 - 这里我们不直接退出，而是隐藏到后台
                if (desktop.isSupported(Desktop.Action.APP_QUIT_HANDLER)) {
                    desktop.setQuitHandler { _, response ->
                        // 不退出应用，而是隐藏到后台
                        hideDockIcon()
                        response.cancelQuit()
                    }
                }
            }

            // 设置 Taskbar 图标（如果支持）
            if (Taskbar.isTaskbarSupported()) {
                val taskbar = Taskbar.getTaskbar()
                if (taskbar.isSupported(Taskbar.Feature.ICON_IMAGE)) {
                    // 这里可以设置 Dock 图标，但我们使用 Compose 的图标资源
                }
            }
        }
    } catch (e: Exception) {
        println("设置 macOS Dock 时出错: ${e.message}")
    }
}

private fun hideDockIcon() {
    try {
        val osName = System.getProperty("os.name").lowercase()
        if (osName.contains("mac")) {
            // 在 macOS 上隐藏 Dock 图标
            SwingUtilities.invokeLater {
                System.setProperty("apple.awt.UIElement", "true")
            }
        }
    } catch (e: Exception) {
        println("隐藏 Dock 图标时出错: ${e.message}")
    }
}

private fun showDockIcon() {
    try {
        val osName = System.getProperty("os.name").lowercase()
        if (osName.contains("mac")) {
            // 在 macOS 上显示 Dock 图标
            SwingUtilities.invokeLater {
                System.setProperty("apple.awt.UIElement", "false")
            }
        }
    } catch (e: Exception) {
        println("显示 Dock 图标时出错: ${e.message}")
    }
}