package to.sunset.litetools

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.window.Tray
import androidx.compose.ui.window.Window
import androidx.compose.ui.window.application
import androidx.compose.ui.window.rememberTrayState
import androidx.compose.ui.window.rememberWindowState
import litetools.composeapp.generated.resources.Res
import litetools.composeapp.generated.resources.icon
import org.jetbrains.compose.resources.painterResource
import java.awt.Desktop
import java.awt.Taskbar
import java.awt.image.BufferedImage
import javax.imageio.ImageIO
import javax.swing.SwingUtilities

fun main() = application {
    val trayState = rememberTrayState()
    val windowState = rememberWindowState()
    var isVisible by remember { mutableStateOf(true) }

    // 设置 Dock 图标
    setupDockIcon()

    Tray(
        state = trayState,
        icon = painterResource(Res.drawable.icon),
        tooltip = "LiteTools",
        onAction = {
            // 左键单击托盘图标显示窗口
            isVisible = true
        },
        menu = {
            Item("退出", onClick = ::exitApplication)
        }
    )

    if (isVisible) {
        Window(
            onCloseRequest = {
                // 关闭窗口时隐藏到后台，而不是退出应用
                isVisible = false
            },
            state = windowState,
            title = "LiteTools",
            icon = painterResource(Res.drawable.icon),
        ) {
            App()
        }
    }
}

private fun setupDockIcon() {
    try {
        // 设置应用程序名称
        System.setProperty("apple.awt.application.name", "LiteTools")

        // 加载图标并设置到 Dock
        SwingUtilities.invokeLater {
            try {
                // 从资源文件加载图标
                val iconStream = Thread.currentThread().contextClassLoader
                    .getResourceAsStream("drawable/icon.png")

                if (iconStream != null) {
                    val iconImage = ImageIO.read(iconStream)

                    // 设置 Taskbar 图标（适用于 macOS Dock）
                    if (Taskbar.isTaskbarSupported()) {
                        val taskbar = Taskbar.getTaskbar()
                        if (taskbar.isSupported(Taskbar.Feature.ICON_IMAGE)) {
                            taskbar.setIconImage(iconImage)
                        }
                    }

                    // 设置 Desktop 图标（备用方法）
                    if (Desktop.isDesktopSupported()) {
                        val desktop = Desktop.getDesktop()
                        // 某些系统可能支持设置应用图标
                    }
                } else {
                    println("无法找到图标文件: drawable/icon.png")
                }
            } catch (e: Exception) {
                println("设置 Dock 图标时出错: ${e.message}")
                e.printStackTrace()
            }
        }
    } catch (e: Exception) {
        println("初始化 Dock 设置时出错: ${e.message}")
    }
}

